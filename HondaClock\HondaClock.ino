#include <Arduino.h>
#include <U8g2lib.h>
#include <RTClib.h>
#include <Wire.h>  // library required for IIC communication
#include <stdio.h>

#define BUTTON_H_PIN 3
#define BUTTON_M_PIN 2
#define BUTTON_RESET_PIN 4

enum Mode { NORMAL,
            SET_HOURS,
            SET_MINUTES };
Mode currentMode = NORMAL;

int hours = 0;
int minutes = 0;

unsigned long buttonPressStart = 0;
bool previusResetButton = HIGH;
const unsigned long longPressDuration = 2000;  // 2 seconds
const unsigned long timeoutDuration = 10000;   // 10 seconds
bool inEditMode = false;

unsigned long lastInteractionTime = 0;

RTC_DS3231 rtc;

U8G2_SSD1306_128X32_UNIVISION_F_HW_I2C u8g2(U8G2_R0, /* reset=*/U8X8_PIN_NONE);  // initialization for the used OLED display
// 's-l1600', 128x32px
const unsigned char epd_bitmap_s_l1600[] PROGMEM = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xff, 0x8f, 0xff, 0x07, 0xff, 0x3f, 0xf8, 0x7f, 0xf8, 0xdf, 0xff, 0xff, 0x01, 0xfe, 0x3f, 0x00,
  0xfe, 0x8f, 0xff, 0xc3, 0xff, 0xff, 0xf0, 0xff, 0xf0, 0xdf, 0xff, 0xff, 0x07, 0xfe, 0x3f, 0x00,
  0xf8, 0x01, 0xfc, 0xe0, 0x0f, 0xfc, 0xc1, 0xff, 0xc1, 0x03, 0x7f, 0xf0, 0x0f, 0xf0, 0x7f, 0x00,
  0xf8, 0x01, 0xfc, 0xf0, 0x07, 0xf8, 0xc3, 0xff, 0xc1, 0x03, 0x3e, 0xc0, 0x1f, 0xf0, 0xfe, 0x00,
  0xf8, 0x01, 0xfc, 0xf0, 0x03, 0xf0, 0xc3, 0xfb, 0xc3, 0x03, 0x3e, 0x80, 0x1f, 0x78, 0xfe, 0x00,
  0xf8, 0xff, 0xff, 0xf8, 0x03, 0xf0, 0xc7, 0xfb, 0xc7, 0x03, 0x3e, 0x80, 0x1f, 0x78, 0xfc, 0x01,
  0xf8, 0xff, 0xff, 0xf8, 0x03, 0xf0, 0xc7, 0xf3, 0xcf, 0x03, 0x3e, 0x80, 0x3f, 0x3c, 0xf8, 0x03,
  0xf8, 0xff, 0xff, 0xf8, 0x03, 0xf0, 0xc7, 0xe3, 0xcf, 0x03, 0x3e, 0x80, 0x3f, 0xfc, 0xff, 0x03,
  0xf8, 0x01, 0xfc, 0xf8, 0x03, 0xf0, 0xc7, 0xc3, 0xdf, 0x03, 0x3e, 0x80, 0x1f, 0xfe, 0xff, 0x07,
  0xf8, 0x01, 0xfc, 0xf0, 0x03, 0xf0, 0xc3, 0xc3, 0xff, 0x03, 0x3e, 0xc0, 0x1f, 0xff, 0xff, 0x0f,
  0xf8, 0x01, 0xfc, 0xf0, 0x07, 0xf8, 0xc3, 0x83, 0xff, 0x03, 0x3e, 0xc0, 0x1f, 0x0f, 0xe0, 0x0f,
  0xf8, 0x01, 0xfc, 0xe0, 0x1f, 0xfe, 0xc1, 0x03, 0xff, 0x03, 0xff, 0xff, 0x8f, 0x07, 0xc0, 0x1f,
  0xff, 0x8f, 0xff, 0xc7, 0xff, 0xff, 0xf8, 0x1f, 0xfe, 0xc3, 0xff, 0xff, 0xf7, 0x3f, 0xf8, 0xff,
  0xff, 0x8f, 0xff, 0x07, 0xff, 0x3f, 0xf8, 0x1f, 0xfe, 0xc3, 0xff, 0xff, 0xf0, 0x3f, 0xf8, 0xff,
  0x00, 0x00, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// 'digit_0', 16x28px
const unsigned char bitmap_digit_0[] PROGMEM = {
  0xf0, 0xff, 0xf8, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xfc, 0x3f, 0xfc,
  0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc,
  0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0xff, 0xff,
  0xff, 0xff, 0xff, 0x7f, 0xff, 0x3f, 0xff, 0x1f
};
// 'digit_1', 16x28px
const unsigned char bitmap_digit_1[] PROGMEM = {
  0x80, 0x1f, 0xc0, 0x1f, 0xe0, 0x1f, 0xf8, 0x1f, 0xf8, 0x1f, 0xf8, 0x1f, 0xf8, 0x1f, 0xf8, 0x1f,
  0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f,
  0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f,
  0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f
};
// 'digit_2', 16x28px
const unsigned char bitmap_digit_2[] PROGMEM = {
  0xf0, 0x7f, 0xf8, 0xff, 0xfc, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xfc, 0x3f, 0xfc,
  0x3f, 0xfc, 0x00, 0xfc, 0x00, 0xfe, 0x00, 0xff, 0x80, 0x7f, 0xc0, 0x3f, 0xf0, 0x1f, 0xf8, 0x0f,
  0xfc, 0x07, 0xfe, 0x03, 0xff, 0x00, 0x7f, 0x00, 0x3f, 0x00, 0x3f, 0x00, 0x3f, 0x00, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
// 'digit_3', 16x28px
const unsigned char bitmap_digit_3[] PROGMEM = {
  0xf0, 0x7f, 0xfc, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xfc, 0x1f, 0xfc,
  0x1f, 0xfc, 0x00, 0xfc, 0x00, 0xfc, 0x00, 0x7f, 0x00, 0x3f, 0x00, 0x1f, 0x00, 0xff, 0x00, 0xff,
  0x00, 0xff, 0x00, 0xfc, 0x00, 0xfc, 0x1f, 0xfc, 0x1f, 0xfc, 0x1f, 0xfc, 0x3f, 0xfc, 0xff, 0xff,
  0xff, 0xff, 0xff, 0x7f, 0xff, 0x3f, 0xff, 0x1f
};
// 'digit_4', 16x28px
const unsigned char bitmap_digit_4[] PROGMEM = {
  0x00, 0x0e, 0x00, 0x1e, 0x00, 0x3f, 0x00, 0x7f, 0x80, 0xff, 0xc0, 0xff, 0xc0, 0xff, 0xe0, 0xff,
  0xe0, 0xff, 0xf0, 0xff, 0xf0, 0xfd, 0xf8, 0xfd, 0xf8, 0xfc, 0xfc, 0xfc, 0x7c, 0xfc, 0x7e, 0xfc,
  0x3e, 0xfc, 0x3f, 0xfc, 0xff, 0xff, 0xfe, 0xff, 0xfc, 0xff, 0xfc, 0xff, 0xf8, 0xff, 0x00, 0xfc,
  0x00, 0xfc, 0x00, 0xfc, 0x00, 0xfc, 0x00, 0xfc
};
// 'digit_5', 16x28px
const unsigned char bitmap_digit_5[] PROGMEM = {
  0xfe, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0x3f, 0x00, 0x3f, 0x00,
  0x3f, 0x00, 0x3f, 0x00, 0x7f, 0x00, 0xff, 0x0f, 0xff, 0x1f, 0xff, 0x7f, 0xff, 0x7f, 0xff, 0xff,
  0x00, 0xfc, 0x00, 0xfc, 0x00, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfe, 0xff, 0xff,
  0xff, 0xff, 0xfe, 0xff, 0xfc, 0xff, 0xf8, 0xff
};
// 'digit_6', 16x28px
const unsigned char bitmap_digit_6[] PROGMEM = {
  0xf0, 0x7f, 0xf8, 0xff, 0xfc, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xfc, 0x3f, 0xfc,
  0x3f, 0xfc, 0x3f, 0x00, 0x3f, 0x00, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfe, 0xff, 0xff,
  0xff, 0x7f, 0xff, 0x3f, 0xff, 0x1f, 0xff, 0x0f
};
// 'digit_7', 16x28px
const unsigned char bitmap_digit_7[] PROGMEM = {
  0xff, 0x0f, 0xff, 0x1f, 0xff, 0x3f, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x1f, 0x7c, 0x1f, 0x7e,
  0x1f, 0x7e, 0x00, 0x3f, 0x00, 0x3f, 0x00, 0x3f, 0x80, 0x1f, 0x80, 0x1f, 0x80, 0x1f, 0xc0, 0x0f,
  0xc0, 0x0f, 0xe0, 0x0f, 0xe0, 0x07, 0xe0, 0x07, 0xf0, 0x03, 0xf0, 0x03, 0xf0, 0x03, 0xf8, 0x01,
  0xf8, 0x01, 0xfc, 0x01, 0xfc, 0x00, 0xfc, 0x00
};
// 'digit_8', 16x28px
const unsigned char bitmap_digit_8[] PROGMEM = {
  0xf0, 0x7f, 0xf8, 0x7f, 0xfc, 0x7f, 0xfe, 0x7f, 0xff, 0x7f, 0xff, 0x7f, 0x3f, 0x7c, 0x3f, 0x7c,
  0x3f, 0x7c, 0x3f, 0x7c, 0x3f, 0x7c, 0x3f, 0x7c, 0xff, 0x3f, 0xff, 0x1f, 0xf8, 0xff, 0xfe, 0xff,
  0xff, 0xff, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0xff, 0xff,
  0xff, 0xff, 0xff, 0x7f, 0xff, 0x3f, 0xff, 0x1f
};
// 'digit_9', 16x28px
const unsigned char bitmap_digit_9[] PROGMEM = {
  0xe0, 0xff, 0xf8, 0xff, 0xfc, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xfc, 0x3f, 0xfc,
  0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0x00, 0xfc, 0x00, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0xff, 0xff,
  0xff, 0xff, 0xff, 0x7f, 0xff, 0x3f, 0xff, 0x1f
};

// Array of all bitmaps for convenience. (Total bytes used to store images in PROGMEM = 800)
const int bitmap_allArray_LEN = 10;
const unsigned char* bitmap_allArray[10] = {
  bitmap_digit_0,
  bitmap_digit_1,
  bitmap_digit_2,
  bitmap_digit_3,
  bitmap_digit_4,
  bitmap_digit_5,
  bitmap_digit_6,
  bitmap_digit_7,
  bitmap_digit_8,
  bitmap_digit_9
};

void hondaLogo(int timeSleep) {
  u8g2.clearBuffer();                                // clear the internal memory
  u8g2.drawXBMP(0, 0, 128, 32, epd_bitmap_s_l1600);  // draw frame of the animation
  u8g2.sendBuffer();                                 // transfer internal memory to the display
  delay(timeSleep * 1000);
}

void clear() {
  u8g2.clear();
}

unsigned long lastButtonPress = 0;
bool resetButtonPressed = false;

void checkButtons() {
  bool resetButton = digitalRead(BUTTON_RESET_PIN) == LOW;
  
  // Gestione del pulsante di reset
  if (resetButton && !resetButtonPressed) {
    resetButtonPressed = true;
    lastButtonPress = millis();
  } else if (!resetButton && resetButtonPressed) {
    resetButtonPressed = false;
    if (millis() - lastButtonPress < longPressDuration) {
      // Pressione breve: cambia modalità
      if (inEditMode) {
        if (currentMode == SET_HOURS) currentMode = SET_MINUTES;
        else if (currentMode == SET_MINUTES) {
          currentMode = NORMAL;
          inEditMode = false;
          saveTimeToRTC();
        }
      }
    }
  }
  
  // Controllo per l'attivazione della modalità di modifica
  if (resetButtonPressed && !inEditMode && millis() - lastButtonPress >= longPressDuration) {
    inEditMode = true;
    currentMode = SET_HOURS;
    lastInteractionTime = millis();
  }
  
  // Gestione dei pulsanti per aumentare/diminuire ore e minuti
  if (inEditMode) {
    if (digitalRead(BUTTON_H_PIN) == LOW) {
      if (currentMode == SET_HOURS) hours = (hours + 1) % 24;
      else if (currentMode == SET_MINUTES) minutes = (minutes + 1) % 60;
      lastInteractionTime = millis();
      delay(200);  // Debounce delay
    }
    
    if (digitalRead(BUTTON_M_PIN) == LOW) {
      if (currentMode == SET_HOURS) hours = (hours - 1 + 24) % 24;
      else if (currentMode == SET_MINUTES) minutes = (minutes - 1 + 60) % 60;
      lastInteractionTime = millis();
      delay(200);  // Debounce delay
    }
  }
  
  // Controllo timeout per uscire dalla modalità di modifica
  if (inEditMode && millis() - lastInteractionTime > timeoutDuration) {
    inEditMode = false;
    currentMode = NORMAL;
    saveTimeToRTC();
  }
}

void clock(int hours, int minutes, int seconds, Mode mode) {
  int offset = 16;
  int space = 16;
  int padding = 3;

  int minuti_unit_x = 112 - padding - offset; // Spostato a sinistra per fare spazio ai secondi
  int minuti_dec_x = minuti_unit_x - space - padding;

  int point_x = minuti_dec_x - space / 2 - 2;

  int ore_unit_x = minuti_dec_x - 32 - padding;
  int ore_dec_x = ore_unit_x - space - padding;

  // Mostra le ore
  u8g2.drawXBMP(ore_unit_x, 2, 16, 28, bitmap_allArray[hours % 10]);
  u8g2.drawXBMP(ore_dec_x, 2, 16, 28, bitmap_allArray[hours / 10]);

  // Disegna la linea sotto le ore se in modalità SET_HOURS
  if (mode == SET_HOURS) {
    u8g2.drawLine(ore_dec_x, 31, ore_unit_x + 16, 31);
  }

  // Lampeggia i due punti ogni secondo
  if (millis() % 2000 < 1000) {
    u8g2.drawCircle(point_x, 8, 2, U8G2_DRAW_ALL);
    u8g2.drawCircle(point_x, 16, 2, U8G2_DRAW_ALL);
  }

  // Mostra i minuti
  u8g2.drawXBMP(minuti_unit_x, 2, 16, 28, bitmap_allArray[minutes % 10]);
  u8g2.drawXBMP(minuti_dec_x, 2, 16, 28, bitmap_allArray[minutes / 10]);

  // Disegna la linea sotto i minuti se in modalità SET_MINUTES
  if (mode == SET_MINUTES) {
    u8g2.drawLine(minuti_dec_x, 31, minuti_unit_x + 16, 31);
  }

  // Aggiungi la data verticalmente a sinistra
  DateTime now = rtc.now();
  char dayStr[3], monthStr[3], yearStr[5];
  sprintf(dayStr, "%02d", now.day());
  sprintf(monthStr, "%02d", now.month());
  sprintf(yearStr, "%04d", now.year());

  u8g2.setFont(u8g2_font_5x7_tf);  // Usa un font piccolo ma leggibile

  // Disegna giorno
  u8g2.drawStr(0, 10, dayStr);
  
  // Disegna mese
  u8g2.drawStr(0, 20, monthStr);
  
  // Disegna anno
  u8g2.drawStr(0, 30, yearStr);

  // Mostra i secondi a destra con metà dimensione
  u8g2.setFont(u8g2_font_7x14_tf);  // Usa un font più grande per i secondi, ma ancora più piccolo dell'orologio principale
  char secondsStr[3];
  sprintf(secondsStr, "%02d", seconds);
  u8g2.drawStr(114, 30, secondsStr);  // Posiziona i secondi in alto a destra
}

void saveTimeToRTC() {
  rtc.adjust(DateTime(rtc.now().year(), rtc.now().month(), rtc.now().day(), hours, minutes, rtc.now().second()));
}
// Variabili per il lampeggio
unsigned long previousMillis = 0;
bool showDots = true;
const long interval = 1000;  // Intervallo di un secondo

void setup() {

  pinMode(BUTTON_H_PIN, INPUT);
  pinMode(BUTTON_M_PIN, INPUT);
  pinMode(BUTTON_RESET_PIN, INPUT);

  Serial.begin(9600);
  u8g2.begin();
  hondaLogo(3);
  // Inizializza l'RTC
  if (!rtc.begin()) {
    u8g2.drawStr(0, 10, "Couldn't find RTC");
    //while (1)
    ;
  }

  // Imposta l'ora se l'RTC ha perso alimentazione
  if (rtc.lostPower()) {
    rtc.adjust(DateTime(F(__DATE__), F(__TIME__)));
  }

  // Inizializza il display
}

void loop() {
  checkButtons();

  DateTime now = rtc.now();
  
  if (currentMode == NORMAL) {
    hours = now.hour();
    minutes = now.minute();
  }
  int seconds = now.second();  // Aggiungiamo i secondi

  // Disegna l'orologio
  u8g2.clearBuffer();
  clock(hours, minutes, seconds, currentMode);
  u8g2.sendBuffer();

  delay(10);  // Un piccolo delay per evitare di sovraccaricare il microcontrollore
}
